<template>
  <web-view
    v-if="httpUrl"
    :src="httpUrl"
    @message="handleWebViewMessage"
  ></web-view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getBaseByEnv } from "@/config";
import { onShow } from "@dcloudio/uni-app";

const httpUrl = ref("");

onShow(buildUrl);

function buildUrl() {
  const _env = uni.getStorageSync("MOBD_EVN_VERSION");

  let url = getBaseByEnv(_env === "release");

  const opt = uni.getEnterOptionsSync();

  const { path, ...other } = opt.query || {};

  if (path) {
    other._ts = Date.now()

    const query = Object.entries(other)
      .map(([key, value]) => `${key}=${value}`)
      .join("&");

    url += `#${path}?${query}`;
  }

  httpUrl.value = url;

  console.log('enter options', opt)
  console.log("build url:", httpUrl.value);
}

// 处理来自网页的消息
function handleWebViewMessage(event: any) {
  console.log('收到网页消息:', event.detail.data);

  const messages = event.detail.data;
  if (!messages || messages.length === 0) return;

  // 处理最新的消息
  const latestMessage = messages[messages.length - 1];

  if (latestMessage.type === 'requestScan') {
    handleScanCode(latestMessage);
  } else {
    console.log('未知消息类型:', latestMessage.type);
  }
}

// 处理扫码请求
function handleScanCode(message: any) {
  console.log('处理扫码请求:', message);

  // 从 config 中获取扫码配置
  const config = message.config || {};

  uni.scanCode({
    scanType: config.scanType || ['qrCode', 'barCode'],
    onlyFromCamera: config.onlyFromCamera || true,
    success: (result: any) => {
      console.log('扫码成功:', result);

      // 向网页发送扫码结果
      postMessageToWebView({
        type: 'scanResult',
        success: true,
        data: {
          result: result.result,
          scanType: result.scanType,
          charSet: result.charSet,
          path: result.path
        }
      });
    },
    fail: (error: any) => {
      console.error('扫码失败:', error);

      // 向网页发送错误信息
      postMessageToWebView({
        type: 'scanResult',
        success: false,
        error: error
      });
    }
  });
}



// 向网页发送扫码结果
function postMessageToWebView(data: any) {
  console.log('向网页发送扫码结果:', data);

  // 在小程序环境中，通常通过 URL 参数或者 storage 的方式与网页通信
  // 这里我们将结果存储到本地，网页可以通过轮询或其他方式获取
  try {
    uni.setStorageSync('scanCodeResult', JSON.stringify(data));

    // 也可以尝试通过修改 URL 的方式通知网页
    const currentUrl = httpUrl.value;
    const separator = currentUrl.includes('?') ? '&' : '?';
    const newUrl = `${currentUrl}${separator}_scanResult=${encodeURIComponent(JSON.stringify(data))}&_ts=${Date.now()}`;

    // 更新 URL 来通知网页
    httpUrl.value = newUrl;
  } catch (error) {
    console.error('发送消息失败:', error);
  }
}
</script>

<style></style>
